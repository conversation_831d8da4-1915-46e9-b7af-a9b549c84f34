import React from 'react';
import { ArrowRight } from 'lucide-react';

const Benchmark = () => {
  const benchmarkData = [
    { name: "AUGMENT", score: 70.6, color: "bg-orange-500", isHighlight: true },
    { name: "REFACT.AI", score: 70.4, color: "bg-amber-600" },
    { name: "OPENHANDS", score: 70.4, color: "bg-yellow-600" },
    { name: "SWE-AGENT", score: 66.6, color: "bg-yellow-700" },
    { name: "DEEPSEEK R1 0528", score: 57.6, color: "bg-amber-800" }
  ];

  return (
    <section className="bg-black text-white py-20 lg:py-32">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid lg:grid-cols-2 gap-16 items-center">
          {/* Left Content */}
          <div>
            <h2 className="text-4xl lg:text-5xl font-bold mb-6 leading-tight">
              Industry leading{' '}
              <span className="block">quality</span>
            </h2>
            
            <p className="text-xl text-gray-300 mb-8 leading-relaxed">
              Proprietary context retrieval combined with cutting-edge 
              frontier models give you code you can ship to production.
            </p>

            <button className="group inline-flex items-center px-6 py-3 border border-gray-600 rounded-lg text-white hover:border-gray-400 transition-all duration-300 hover:bg-gray-900">
              <span className="mr-3">READ MORE: A REAL TIME INDEX OF YOUR CODEBASE</span>
              <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" />
            </button>
          </div>

          {/* Right Content - Benchmark Chart */}
          <div className="relative">
            <div className="bg-gray-900 rounded-2xl p-8 border border-gray-800 shadow-2xl">
              <h3 className="text-lg font-mono text-gray-300 mb-8 tracking-wider">
                SWE-BENCH VERIFIED OPEN SOURCE
              </h3>
              
              <div className="space-y-6">
                {benchmarkData.map((item, index) => (
                  <div 
                    key={index}
                    className="group cursor-pointer"
                  >
                    {/* Score and Name */}
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-mono text-gray-300">
                        {item.score}%
                      </span>
                    </div>
                    
                    {/* Progress Bar */}
                    <div className="relative mb-3">
                      <div className="w-full bg-gray-800 rounded-sm h-8 overflow-hidden">
                        <div 
                          className={`h-full ${item.color} transition-all duration-700 ease-out group-hover:brightness-110 ${
                            item.isHighlight ? 'shadow-lg shadow-orange-500/30' : ''
                          }`}
                          style={{ 
                            width: `${item.score}%`,
                            background: item.isHighlight 
                              ? 'linear-gradient(90deg, #f97316 0%, #ea580c 100%)'
                              : undefined
                          }}
                        />
                      </div>
                      
                      {/* Animated overlay on hover */}
                      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000 ease-in-out" />
                    </div>
                    
                    {/* Company Name */}
                    <div className="text-sm font-mono text-gray-400 group-hover:text-gray-200 transition-colors duration-300">
                      {item.name}
                    </div>
                  </div>
                ))}
              </div>
              
              {/* Subtle glow effect */}
              <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-orange-500/5 to-transparent pointer-events-none" />
            </div>
            
            {/* Background decoration */}
            <div className="absolute -inset-4 bg-gradient-to-br from-orange-500/10 to-transparent rounded-3xl blur-xl opacity-50" />
          </div>
        </div>
      </div>
    </section>
  );
};

export default Benchmark;
